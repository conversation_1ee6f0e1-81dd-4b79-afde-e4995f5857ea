<script setup lang="ts">
import { useToast } from "primevue/usetoast";
import { onMounted, ref, nextTick } from "vue";
import { usePermission } from "../../composables/usePermission";
import {
  getBankStatements,
  updateBankStatement,
} from "../../services/bankStatement";
import { getCustomersSimpleList } from "../../services/customer";
import type {
  BankStatementItem,
  BankStatementFormData,
  BankStatementParams,
  BankStatementUpdateFormData,
} from "../../types/bankStatement";
import type { CustomerSimpleInfo } from "../../types/customer";
import { formatDateTime, formatCurrency } from "../../utils/common";
import {
  bankStatementStateOptions,
  bankStatementStateMap,
  bankStatementStateSeverityMap,
} from "../../utils/const";
import { optionLoaders } from "../../utils/options";
import RecognitionDetail from "./RecognitionDetail.vue";

const bankStatements = ref<BankStatementItem[]>([]);
const loading = ref(false);
const totalRecords = ref(0);
const toast = useToast();

// 使用权限管理组合式函数
const { hasOperationPermission, initializeUserInfo } = usePermission();

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 筛选参数
const filterState = ref<number | null>(null);
const filterStatementNo = ref("");
const filterPaymentName = ref("");

// 加载银行流水列表
const loadBankStatements = async () => {
  loading.value = true;
  try {
    const params: BankStatementParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    if (filterState.value !== null) {
      params.receipt_status = filterState.value;
    }
    if (filterStatementNo.value.trim()) {
      params.statement_no = filterStatementNo.value.trim();
    }
    if (filterPaymentName.value.trim()) {
      params.payment_name = filterPaymentName.value.trim();
    }

    const response = await getBankStatements(params);
    bankStatements.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载银行流水失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadBankStatements();
};

// 搜索
const handleSearch = () => {
  lazyParams.value.page = 1;
  loadBankStatements();
};

// 重置筛选
const resetFilters = () => {
  filterState.value = null;
  filterStatementNo.value = "";
  filterPaymentName.value = "";
  lazyParams.value.page = 1;
  loadBankStatements();
};

onMounted(async () => {
  await initializeUserInfo();
  loadCurrencyOptions();
  loadCustomerOptions();
  loadBankStatements();
});

// 表单相关
const bankStatementDrawerVisible = ref(false);
const editingBankStatement = ref<BankStatementItem | null>(null);
const isSubmitting = ref(false);
const submitted = ref(false);
const fieldErrors = ref<Record<string, string>>({});

const bankStatementForm = ref<BankStatementFormData>({
  amount: null,
  currency_type: "",
  statement_no: "",
  statement_date: null,
  payment_name: "",
  payment_bank_no: "",
  payment_bank_name: "",
  receive_bank_name: "",
  receive_bank_no: "",
  confirming_amount: null,
  confirmed_amount: null,
  customer_num: "",
  customer_name: "",
  description: "",
});

const bankStatementUpdateForm = ref<BankStatementUpdateFormData>({
  customer_num: "",
  customer_name: "",
});

// 货币类型选项
const currencyOptions = ref<{ label: string; value: string }[]>([]);
const loadCurrencyOptions = () => optionLoaders.currencyType(currencyOptions);

// 客户选项
const customerOptions = ref<{ label: string; value: string }[]>([]);
const loadCustomerOptions = async () => {
  try {
    const response = await getCustomersSimpleList();
    customerOptions.value = response.data.map((item: CustomerSimpleInfo) => ({
      label: `${item.customer_name} (${item.customer_num})`,
      value: item.customer_num,
    }));
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载客户列表失败",
      life: 3000,
    });
  }
};

// 客户选择变化处理
const onCustomerChange = (event: any) => {
  const selectedCustomerNum = event.value;
  if (selectedCustomerNum) {
    const selectedCustomer = customerOptions.value.find(
      (option) => option.value === selectedCustomerNum
    );
    if (selectedCustomer) {
      // 从label中提取客户名称（格式：客户名称 (客户编号)）
      const customerName = selectedCustomer.label.split(" (")[0];
      bankStatementUpdateForm.value.customer_name = customerName;
    }
  } else {
    bankStatementUpdateForm.value.customer_name = "";
  }
};

// 清除字段错误
const clearFieldErrors = () => {
  fieldErrors.value = {};
  submitted.value = false;
};

// 编辑银行流水
const editBankStatement = (bankStatement: BankStatementItem) => {
  editingBankStatement.value = bankStatement;
  bankStatementForm.value = {
    amount: parseFloat(bankStatement.amount) || null,
    currency_type: bankStatement.currency_type,
    statement_no: bankStatement.statement_no,
    statement_date: bankStatement.statement_date
      ? new Date(bankStatement.statement_date)
      : null,
    payment_name: bankStatement.payment_name,
    payment_bank_no: bankStatement.payment_bank_no,
    payment_bank_name: bankStatement.payment_bank_name,
    receive_bank_name: bankStatement.receive_bank_name,
    receive_bank_no: bankStatement.receive_bank_no,
    confirming_amount: parseFloat(bankStatement.confirming_amount) || null,
    confirmed_amount: parseFloat(bankStatement.confirmed_amount) || null,
    customer_num: bankStatement.customer_num || "",
    customer_name: bankStatement.customer_name || "",
    description: bankStatement.description,
  };
  bankStatementUpdateForm.value = {
    customer_num: bankStatement.customer_num || "",
    customer_name: bankStatement.customer_name || "",
  };
  clearFieldErrors();
  bankStatementDrawerVisible.value = true;
};

// 验证必填字段
const validateForm = () => {
  const errors: Record<string, string> = {};

  // 只验证客户选择是否为空
  if (
    !bankStatementUpdateForm.value.customer_num ||
    !bankStatementUpdateForm.value.customer_num.trim()
  ) {
    errors.customer_num = "请选择客户";
  }

  fieldErrors.value = errors;
  return Object.keys(errors).length === 0;
};

// 保存银行流水
const saveBankStatement = async () => {
  submitted.value = true;

  if (!validateForm()) {
    toast.add({
      severity: "error",
      summary: "验证失败",
      detail: "请选择客户",
      life: 3000,
    });
    return;
  }

  isSubmitting.value = true;
  try {
    await updateBankStatement(
      editingBankStatement.value!.id,
      bankStatementUpdateForm.value
    );
    toast.add({
      severity: "success",
      summary: "成功",
      detail: "银行流水更新成功",
      life: 3000,
    });
    bankStatementDrawerVisible.value = false;
    clearFieldErrors();
    loadBankStatements();
  } catch (error: any) {
    // 处理422验证错误
    if (error.response?.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: "保存银行流水失败",
        life: 3000,
      });
    }
  } finally {
    isSubmitting.value = false;
  }
};

// 关闭抽屉
const closeDrawer = () => {
  bankStatementDrawerVisible.value = false;
  clearFieldErrors();
};

// Tab管理
const activeTab = ref("list");
const tabs = ref<
  {
    id: string;
    title: string;
    type: "list" | "recognition";
    bankStatementId?: number;
    statementNo?: string;
  }[]
>([{ id: "list", title: "银行流水列表", type: "list" }]);

// 添加新的 Tab 页
const addTab = (type: "recognition", bankStatement: BankStatementItem) => {
  // 查找是否存在同类型的标签页
  const existingTabIndex = tabs.value.findIndex((tab) => {
    return tab.id === `${type}-${bankStatement.id}`;
  });
  // 如果存在同类型标签页
  if (existingTabIndex > -1) {
    // 先将activeTab设为null，然后再设置为目标值，确保Vue能检测到变化
    const targetTabId = tabs.value[existingTabIndex].id;
    if (activeTab.value === targetTabId) {
      activeTab.value = "temp-force-update";
      // 使用nextTick确保DOM更新后再切换回目标Tab
      nextTick(() => {
        activeTab.value = targetTabId;
      });
    } else {
      activeTab.value = targetTabId;
    }
    return;
  }

  // 如果不存在，创建新标签页
  const newTab = {
    id: `${type}-${bankStatement.id}`,
    title: "认款详情",
    type,
    bankStatementId: bankStatement.id,
    statementNo: bankStatement.statement_no,
  };
  tabs.value.push(newTab);
  activeTab.value = newTab.id;
};

// 关闭 Tab 页
const closeTab = (tabId: string) => {
  const index = tabs.value.findIndex((tab) => tab.id === tabId);
  if (index > -1) {
    tabs.value.splice(index, 1);
    // 如果关闭的是当前标签，切换到列表页
    if (activeTab.value === tabId) {
      activeTab.value = "list";
    } else {
      // 如果关闭的不是当前标签，切换到上一个标签
      activeTab.value = "temp-force-update";
      // 使用nextTick确保DOM更新后再切换回目标Tab
      nextTick(() => {
        activeTab.value = tabs.value[tabs.value.length - 1].id;
      });
    }
  }
};

// 认款操作
const handleRecognition = (bankStatement: BankStatementItem) => {
  addTab("recognition", bankStatement);
};
</script>

<template>
  <div class="bank-flow-container">
    <div class="card">
      <Tabs
        :value="activeTab"
        @update:modelValue="activeTab = $event"
        :scrollable="true"
        class="mb-2"
      >
        <TabList>
          <Tab
            v-for="tab in tabs.filter((t) => t.id !== 'temp-force-update')"
            :key="tab.id"
            :value="tab.id"
          >
            <span
              >{{ tab.title
              }}{{ tab.statementNo ? ` - ${tab.statementNo}` : "" }}</span
            >
            <Button
              v-if="tab.type !== 'list'"
              icon="pi pi-times"
              text
              rounded
              severity="danger"
              size="small"
              @click.stop="closeTab(tab.id)"
              class="close-button p-0"
            />
          </Tab>
        </TabList>
        <TabPanels>
          <TabPanel
            v-for="tab in tabs.filter((t) => t.id !== 'temp-force-update')"
            :key="tab.id"
            :value="tab.id"
          >
            <!-- 银行流水列表内容 -->
            <div v-if="tab.type === 'list'">
              <!-- 筛选区域 -->
              <Toolbar class="mb-2">
                <template #end>
                  <div class="flex flex-wrap align-items-center gap-2">
                    <FloatLabel>
                      <label for="filterState">流水状态</label>
                      <Select
                        id="filterState"
                        v-model="filterState"
                        :options="bankStatementStateOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="选择流水状态"
                        showClear
                        style="min-width: 12rem"
                      />
                    </FloatLabel>
                    <FloatLabel>
                      <label for="filterStatementNo">流水号</label>
                      <InputText id="filterStatementNo" v-model="filterStatementNo" />
                    </FloatLabel>
                    <FloatLabel class="mr-2">
                      <label for="filterPaymentName">付款方名称</label>
                      <InputText id="filterPaymentName" v-model="filterPaymentName" />
                    </FloatLabel>
                  </div>
                  <Button
                    label="搜索"
                    icon="pi pi-search"
                    @click="handleSearch"
                    class="mr-2 p-button-sm"
                  />
                  <Button
                    label="重置"
                    icon="pi pi-refresh"
                    @click="resetFilters"
                    outlined
                    class="p-button-sm p-button-outlined"
                  />
                </template>
              </Toolbar>

              <!-- 数据表格 -->
              <DataTable
                :value="bankStatements"
                :lazy="true"
                :paginator="true"
                :rows="20"
                :rowsPerPageOptions="[10, 20, 50]"
                :totalRecords="totalRecords"
                :loading="loading"
                @page="onPage($event)"
                class="p-datatable-sm"
                showGridlines
                stripedRows
                scrollable
                scrollHeight="calc(100vh - 28rem)"
              >
        <template #empty>
          <div class="empty-message">
            <i
              class="pi pi-inbox"
              style="
                font-size: 2rem;
                color: var(--p-text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>暂无银行流水数据</p>
          </div>
        </template>
        <Column field="statement_no" header="流水号" style="min-width: 15rem" />
        <Column field="amount" header="金额" style="min-width: 12rem">
          <template #body="slotProps">
            <span>{{
              formatCurrency(
                slotProps.data.amount,
                slotProps.data.currency_type
              )
            }}</span>
          </template>
        </Column>
        <Column
          field="statement_date"
          header="流水日期"
          style="min-width: 10rem"
        />
        <Column
          field="payment_name"
          header="付款方名称"
          style="min-width: 20rem"
        />
        <Column
          field="payment_bank_name"
          header="付款银行"
          style="min-width: 20rem"
        />
        <Column
          field="receive_bank_name"
          header="收款银行"
          style="min-width: 20rem"
        />
        <Column
          field="customer_name"
          header="客户名称"
          style="min-width: 15rem"
        >
          <template #body="slotProps">
            <span>{{ slotProps.data.customer_name || "--" }}</span>
          </template>
        </Column>
        <Column field="created_at" header="创建时间" style="min-width: 15rem">
          <template #body="slotProps">
            <span>{{ formatDateTime(slotProps.data.created_at) }}</span>
          </template>
        </Column>
        <Column field="receipt_status" header="流水状态">
          <template #body="slotProps">
            <Tag
              :value="bankStatementStateMap[slotProps.data.receipt_status]"
              :severity="
                bankStatementStateSeverityMap[slotProps.data.receipt_status]
              "
            />
          </template>
        </Column>
        <Column header="操作" :exportable="false" style="min-width: 8rem">
          <template #body="slotProps">
            <Button
              icon="pi pi-pencil"
              rounded
              outlined
              @click="editBankStatement(slotProps.data)"
              v-tooltip.top="'编辑'"
              :disabled="
                !hasOperationPermission ||
                (slotProps.data.customer_num !== null &&
                  slotProps.data.customer_num !== '')
              "
              class="mr-2"
            />
            <Button
              icon="fa fa-regular fa-handshake"
              rounded
              outlined
              severity="warn"
              v-tooltip.top="'认款'"
              :disabled="
                !hasOperationPermission ||
                (slotProps.data.customer_num === null ||
                  slotProps.data.customer_num === '')
              "
              @click="handleRecognition(slotProps.data)"
            />
          </template>
        </Column>
              </DataTable>
            </div>

            <!-- 认款详情内容 -->
            <div
              v-if="tab.type === 'recognition' && tab.bankStatementId"
              class="recognition-detail"
            >
              <RecognitionDetail :bankStatementId="tab.bankStatementId" />
            </div>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </div>

    <!-- 编辑抽屉 -->
    <Drawer
      v-model:visible="bankStatementDrawerVisible"
      position="right"
      :style="{ width: '70rem' }"
      :modal="true"
      :closable="true"
      :dismissable="true"
      :showCloseIcon="true"
      :header="`编辑银行流水 - ${editingBankStatement?.statement_no || ''}`"
      class="bank-statement-drawer p-fluid"
      @hide="closeDrawer"
    >
      <div v-if="editingBankStatement" class="p-4">
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">基本信息</h3>
            <Divider />
          </div>
          <div class="section-content">
            <Fluid>
              <div class="grid grid-cols-2 gap-4">
                <div class="field">
                  <label for="amount">金额</label>
                  <InputNumber
                    id="amount"
                    v-model="bankStatementForm.amount"
                    mode="decimal"
                    :minFractionDigits="2"
                    :maxFractionDigits="2"
                    readonly
                    disabled
                  />
                </div>
                <div class="field">
                  <label for="currency_type">货币类型</label>
                  <InputText
                    id="currency_type"
                    v-model="bankStatementForm.currency_type"
                    readonly
                    disabled
                  />
                </div>
                <div class="field">
                  <label for="statement_no">流水号</label>
                  <InputText
                    id="statement_no"
                    v-model="bankStatementForm.statement_no"
                    readonly
                    disabled
                  />
                </div>
                <div class="field">
                  <label for="statement_date">流水日期</label>
                  <DatePicker
                    id="statement_date"
                    v-model="bankStatementForm.statement_date"
                    dateFormat="yy-mm-dd"
                    readonly
                    disabled
                  />
                </div>
                <div class="field">
                  <label for="payment_name">付款方名称</label>
                  <InputText
                    id="payment_name"
                    v-model="bankStatementForm.payment_name"
                    readonly
                    disabled
                  />
                </div>
                <div class="field">
                  <label for="payment_bank_no">付款银行账号</label>
                  <InputText
                    id="payment_bank_no"
                    v-model="bankStatementForm.payment_bank_no"
                    readonly
                    disabled
                  />
                </div>
                <div class="field">
                  <label for="payment_bank_name">付款银行名称</label>
                  <InputText
                    id="payment_bank_name"
                    v-model="bankStatementForm.payment_bank_name"
                    readonly
                    disabled
                  />
                </div>
                <div class="field">
                  <label for="receive_bank_name">收款银行名称</label>
                  <InputText
                    id="receive_bank_name"
                    v-model="bankStatementForm.receive_bank_name"
                    readonly
                    disabled
                  />
                </div>
                <div class="field">
                  <label for="receive_bank_no">收款银行账号</label>
                  <InputText
                    id="receive_bank_no"
                    v-model="bankStatementForm.receive_bank_no"
                    readonly
                    disabled
                  />
                </div>
                <div class="field">
                  <label for="confirming_amount">待确认金额</label>
                  <InputNumber
                    id="confirming_amount"
                    v-model="bankStatementForm.confirming_amount"
                    mode="decimal"
                    :minFractionDigits="2"
                    :maxFractionDigits="2"
                    readonly
                    disabled
                  />
                </div>
                <div class="field">
                  <label for="confirmed_amount">已确认金额</label>
                  <InputNumber
                    id="confirmed_amount"
                    v-model="bankStatementForm.confirmed_amount"
                    mode="decimal"
                    :minFractionDigits="2"
                    :maxFractionDigits="2"
                    readonly
                    disabled
                  />
                </div>
                <div class="field col-span-2">
                  <label for="description">备注</label>
                  <Textarea
                    id="description"
                    v-model="bankStatementForm.description"
                    readonly
                    disabled
                  />
                </div>
              </div>
            </Fluid>
          </div>
        </div>

        <!-- 客户信息 -->
        <div class="form-section">
          <div class="section-header">
            <h3 class="section-title">客户信息</h3>
            <Divider />
          </div>
          <div class="section-content">
            <Fluid>
              <div class="grid grid-cols-2 gap-4">
                <div class="field">
                  <label for="customer_select" class="required">选择客户</label>
                  <Select
                    id="customer_select"
                    v-model="bankStatementUpdateForm.customer_num"
                    :options="customerOptions"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="请选择客户"
                    filter
                    :class="{ 'p-invalid': fieldErrors.customer_num }"
                    @change="onCustomerChange"
                  />
                  <small class="p-error" v-if="fieldErrors.customer_num">
                    {{ fieldErrors.customer_num }}
                  </small>
                </div>
                <div class="field">
                  <label for="customer_name">客户名称</label>
                  <InputText
                    id="customer_name"
                    v-model="bankStatementUpdateForm.customer_name"
                    readonly
                    disabled
                  />
                </div>
              </div>
            </Fluid>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-start gap-2">
          <Button
            label="取消"
            icon="pi pi-times"
            @click="closeDrawer"
            outlined
            :disabled="isSubmitting"
          />
          <Button
            label="保存"
            icon="pi pi-check"
            @click="saveBankStatement"
            :loading="isSubmitting"
          />
        </div>
      </template>
    </Drawer>
  </div>
</template>

<style scoped>
.bank-flow-container {
  padding: 1rem;
  background: #f8f9fa;
  height: calc(100vh - 10rem);
}

.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-header {
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--p-text-color);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--p-surface-ground);
  border-radius: 6px;
}

.empty-message p {
  margin: 0;
  color: var(--p-text-color-secondary);
  font-size: 1.1rem;
}

.form-section {
  margin-bottom: 2rem;
  border-radius: 12px;
  border: 1px solid var(--surface-border);
  overflow: hidden;
}

.section-header {
  padding: 1.5rem 1.5rem 0 1.5rem;
  background: var(--p-surface-card);
}

.section-content {
  padding: 0 1.5rem 1.5rem 1.5rem;
}

.section-title {
  margin: 0 0 1rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color);
  letter-spacing: -0.025em;
}

.field {
  margin-bottom: 0.5rem;
}

.field label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--p-primary-color);
  letter-spacing: -0.025em;
}

.field label.required::after {
  content: " *";
  color: #ff3b30;
  font-weight: 600;
  margin-left: 2px;
}

/* DataTable样式优化 */
:deep(.p-datatable) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

:deep(.p-datatable .p-datatable-header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 1rem;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: #f8f9fa !important;
  border-bottom: 2px solid #e9ecef !important;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  border-bottom: 1px solid #f1f1f1 !important;
  font-size: 0.9rem !important;
  color: #1d1d1f !important;
  padding: 0.5rem 1rem !important;
}

:deep(.p-button-sm) {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

:deep(.p-drawer .p-drawer-header) {
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

:deep(.p-drawer .p-drawer-content) {
  padding: 0;
}

:deep(.p-drawer .p-drawer-footer) {
  background: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 1rem;
}

/* 银行流水抽屉样式 - 参考OrderList.vue */
:deep(.bank-statement-drawer) {
  .p-drawer-content {
    display: flex;
    flex-direction: column;
  }
}

/* 表单验证错误样式 */
.p-error {
  color: #ff3b30;
  font-size: 0.75rem;
  font-weight: 400;
  margin-top: 0.25rem;
  display: block;
  line-height: 1.2;
}

:deep(.p-invalid) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 1px #ff3b30 !important;
}

:deep(.p-invalid:focus) {
  border-color: #ff3b30 !important;
  box-shadow: 0 0 0 3px rgba(255, 59, 48, 0.1) !important;
}

/* 提交按钮加载状态 */
:deep(.p-button:disabled) {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Tab样式 */
:deep(.p-tabview-nav) {
  border: none;
  background: transparent;
  margin-bottom: 1rem;
}

:deep(.p-tabview-nav-link) {
  background: var(--surface-ground);
  border: 1px solid var(--surface-border);
  border-radius: 6px;
  margin-right: 0.5rem;
  padding: 0.75rem 1rem;
  transition: all 0.2s;
  user-select: none;
}

:deep(.p-tabview-selected .p-tabview-nav-link) {
  background: var(--surface-card);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

:deep(.p-tabview-nav-link:not(.p-disabled):focus) {
  box-shadow: none;
  border-color: var(--primary-color);
}

:deep(.p-tabview-panels) {
  background: var(--surface-card);
  border-radius: 6px;
  padding: 1.5rem;
}

.close-button {
  opacity: 0.7;
  transition: opacity 0.2s;
}

.close-button:hover {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }

  .section-header,
  .section-content {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .p-error {
    font-size: 0.7rem;
  }
}
</style>
