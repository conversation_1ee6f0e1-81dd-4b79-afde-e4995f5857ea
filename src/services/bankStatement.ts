import { ApiListResponse, ApiResponse } from "../types/api";
import {
  BankStatementItem,
  BankStatementUpdateFormData,
  BankStatementParams,
} from "../types/bankStatement";
import api from "./api";

// 获取银行流水列表
export const getBankStatements = async (
  params: BankStatementParams
): Promise<ApiListResponse<BankStatementItem[]>> => {
  const response = await api.get("/bank-statements", { params });
  return response.data;
};

// 更新银行流水
export const updateBankStatement = async (
  id: number,
  data: BankStatementUpdateFormData
): Promise<ApiResponse<any>> => {
  const response = await api.put(`/bank-statements/${id}`, data);
  return response.data;
};
