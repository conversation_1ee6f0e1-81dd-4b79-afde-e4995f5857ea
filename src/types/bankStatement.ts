export interface BankStatementItem {
  id: number;
  amount: string;
  currency_type: string;
  statement_no: string;
  statement_date: string;
  payment_name: string;
  payment_bank_no: string;
  payment_bank_name: string;
  receive_bank_name: string;
  receive_bank_no: string;
  confirming_amount: string;
  confirmed_amount: string;
  customer_num: string | null;
  customer_name: string | null;
  description: string;
  receipt_status: number;
  created_at: string;
}

export interface BankStatementFormData {
  amount: number | null;
  currency_type: string;
  statement_no: string;
  statement_date: Date | null;
  payment_name: string;
  payment_bank_no: string;
  payment_bank_name: string;
  receive_bank_name: string;
  receive_bank_no: string;
  confirming_amount: number | null;
  confirmed_amount: number | null;
  customer_num: string;
  customer_name: string;
  description: string;
}

export interface BankStatementUpdateFormData {
  customer_num: string;
  customer_name: string;
}

export interface BankStatementParams {
  page?: number;
  pageSize?: number;
  receipt_status?: number;
  statement_no?: string;
  payment_name?: string;
}
